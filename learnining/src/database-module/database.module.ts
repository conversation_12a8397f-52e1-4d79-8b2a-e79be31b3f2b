import { Module, DynamicModule } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MongooseModule } from '@nestjs/mongoose';
import { getDatabaseConfig, DatabaseConfig } from '../config/database.config';
import { PostgresConfig } from 'src/common/interface/postgresInterface';
import { MongoConfig } from 'src/common/interface/mongodbInterface';

@Module({})
export class DatabaseModule {
  static forRoot(): DynamicModule {
    const dbConfig: DatabaseConfig = getDatabaseConfig();

    if (isPostgresConfig(dbConfig)) {
      return {
        module: DatabaseModule,
        imports: [
          TypeOrmModule.forRoot({
            type: 'postgres',
            host: dbConfig.host,
            port: dbConfig.port,
            username: dbConfig.username,
            password: dbConfig.password,
            database: dbConfig.database,
            autoLoadEntities: true,
            synchronize: process.env.NODE_ENV !== 'production',
            logging: process.env.NODE_ENV !== 'production',
          }),
        ],
      };
    }

    if (isMongoConfig(dbConfig)) {
      return {
        module: DatabaseModule,
        // eslint-disable-next-line @typescript-eslint/no-unsafe-call, @typescript-eslint/no-unsafe-member-access
        imports: [(MongooseModule as any).forRoot(dbConfig.uri)],
      };
    }

    throw new Error('Unsupported DB_TYPE in environment config');
  }
}

function isPostgresConfig(config: DatabaseConfig): config is PostgresConfig {
  return config.type === 'postgres';
}

function isMongoConfig(config: DatabaseConfig): config is MongoConfig {
  return config.type === 'mongodb';
}
